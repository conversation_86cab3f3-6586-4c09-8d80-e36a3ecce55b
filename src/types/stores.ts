// Store-specific TypeScript definitions

import { 
  CandleData, 
  TickData, 
  DataType, 
  Timeframe, 
  UpdateMode, 
  TradingSettings,
  Order,
  Position,
  Account,
  PerformanceMetrics,
  OrderType,
  CSVParseResult,
  DragLineType
} from './trading';

// Base store interface
export interface BaseStore {
  lastUpdated: number;
  version: number;
}

// Trading Session Store Types
export interface TradingSessionStoreState extends BaseStore {
  // Session data
  symbol: string;
  data: CandleData[];
  baseData: CandleData[];
  tickData?: TickData[];
  dataType: DataType;
  
  // Playback state
  currentIndex: number;
  isPlaying: boolean;
  playbackSpeed: number | 'realtime';
  
  // Market data
  currentBid: number;
  currentAsk: number;
  spread: number;
  lastKnownBid: number;
  lastKnownAsk: number;
  precision?: number;
  
  // Display settings
  timeframe: Timeframe;
  updateMode: UpdateMode;

  // Intra-candle progress
  intraCandleProgress?: { step: number; total: number; progress: number };

  // Settings
  settings: TradingSettings;
  
  // UI state
  showImporter: boolean;
  showSettings: boolean;
}

export interface TradingSessionStoreActions {
  // Data management
  setData: (data: CandleData[], baseData: CandleData[], tickData?: TickData[]) => void;
  setSymbol: (symbol: string) => void;
  setDataType: (dataType: DataType) => void;
  setPrecision: (precision: number) => void;
  
  // Playback controls
  setCurrentIndex: (index: number) => void;
  setIsPlaying: (isPlaying: boolean) => void;
  setPlaybackSpeed: (speed: number | 'realtime') => void;
  stepForward: () => void;
  stepBackward: () => void;
  resetSession: () => void;
  
  // Market data updates
  updateMarketData: (bid: number, ask: number, spread: number) => void;
  setLastKnownPrices: (bid: number, ask: number) => void;
  
  // Display settings
  setTimeframe: (timeframe: Timeframe) => void;
  setUpdateMode: (updateMode: UpdateMode) => void;
  setIntraCandleProgress: (progress?: { step: number; total: number; progress: number }) => void;

  // Settings
  updateSettings: (settings: Partial<TradingSettings>) => void;
  
  // UI state
  setShowImporter: (show: boolean) => void;
  setShowSettings: (show: boolean) => void;
}

// Account Store Types
export interface AccountStoreState extends BaseStore {
  account: Account;
  orders: Order[];
  positions: Position[];
  startBalance: number;
}

export interface AccountStoreActions {
  // Account management
  updateAccount: (account: Partial<Account>) => void;
  resetAccount: (balance: number) => void;
  
  // Order management
  addOrder: (order: Order) => void;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  removeOrder: (orderId: string) => void;
  clearOrders: () => void;
  
  // Position management
  addPosition: (position: Position) => void;
  updatePosition: (positionId: string, updates: Partial<Position>) => void;
  removePosition: (positionId: string) => void;
  clearPositions: () => void;
  
  // Bulk operations
  processOrderExecution: (order: Order, executionPrice: number) => void;
  updatePositionPnL: (positionId: string, currentPrice: number) => void;
}

// Chart Interaction Store Types
export interface ChartInteractionStoreState extends BaseStore {
  // Fibonacci tools
  fibonacciLines: Array<{
    id: string;
    startPrice: number;
    endPrice: number;
    startTime: number;
    endTime: number;
  }>;
  
  // Drawing tools
  trendLines: Array<{
    id: string;
    startPrice: number;
    endPrice: number;
    startTime: number;
    endTime: number;
  }>;
  
  // Drag and drop state
  isDragging: boolean;
  draggedOrderId?: string;
  draggedPositionId?: string;
  
  // Context menu state
  contextMenu: {
    visible: boolean;
    x: number;
    y: number;
    price: number;
    time: number;
  };
  
  // Chart settings
  enableFibonacci: boolean;
  enableDragAndDrop: boolean;
}

export interface ChartInteractionStoreActions {
  // Fibonacci tools
  addFibonacciLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  removeFibonacciLine: (id: string) => void;
  clearFibonacciLines: () => void;
  
  // Trend lines
  addTrendLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  removeTrendLine: (id: string) => void;
  clearTrendLines: () => void;
  
  // Drag and drop
  startDrag: (orderId?: string, positionId?: string) => void;
  endDrag: () => void;
  
  // Context menu
  showContextMenu: (x: number, y: number, price: number, time: number) => void;
  hideContextMenu: () => void;
  
  // Chart settings
  setEnableFibonacci: (enable: boolean) => void;
  setEnableDragAndDrop: (enable: boolean) => void;
}

// Combined store types
export type TradingSessionStore = TradingSessionStoreState & TradingSessionStoreActions;
export type AccountStore = AccountStoreState & AccountStoreActions;
export type ChartInteractionStore = ChartInteractionStoreState & ChartInteractionStoreActions;

// Hook return types for better type safety and IntelliSense
export interface UseTradingSessionReturn extends TradingSessionStoreState, TradingSessionStoreActions {
  // Computed values
  totalItems: number;
  hasData: boolean;
  canStepForward: boolean;
  canStepBackward: boolean;
  
  // Enhanced actions
  handleDataLoaded: (result: CSVParseResult) => void;
  switchTimeframe: (timeframe: string) => void;
  startPlayback: () => void;
  stopPlayback: () => void;
  togglePlayback: () => void;
}

export interface UseAccountReturn extends AccountStoreState, AccountStoreActions {
  // Computed values
  totalUnrealizedPnL: number;
  performanceMetrics: PerformanceMetrics;
  
  // Enhanced actions
  placeOrder: (params: {
    type: OrderType;
    size: number;
    price?: number;
    stopLoss?: number;
    takeProfit?: number;
    currentBid: number;
    currentAsk: number;
    precision: number;
  }) => Order;
  closePosition: (positionId: string, currentBid: number, currentAsk: number) => void;
  updatePositionLevels: (positionId: string, stopLoss?: number, takeProfit?: number, currentBid?: number, currentAsk?: number) => void;
  checkPendingOrders: (currentBid: number, currentAsk: number) => void;
  checkStopLossTakeProfit: (currentBid: number, currentAsk: number) => void;
  updateAllPositionPnLs: (currentBid: number, currentAsk: number) => void;
  resetAccount: (initialBalance?: number) => void;
}

export interface UseChartInteractionReturn extends ChartInteractionStoreState, ChartInteractionStoreActions {
  // Chart reference management
  setChartRef: (chart: any) => void;
  chartRef: any;
  
  // Event handlers
  handleRightClick: (param: any) => void;
  handleContextMenuAction: (action: string, price: number, time: number) => void;
  handleMouseDown: (param: any) => void;
  handleMouseMove: (param: any) => void;
  handleMouseUp: (param: any) => void;
  setupChartEventHandlers: (chart: any) => () => void;
  
  // Drawing tools
  startFibonacciDrawing: () => void;
  addFibonacciRetracement: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  startTrendLineDrawing: () => void;
  addTrendLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  clearAllDrawings: () => void;
  
  // Utilities
  coordinateToPrice: (coordinate: number) => number | null;
  priceToCoordinate: (price: number) => number | null;
}

// Store selector types for optimized subscriptions
export type TradingSessionSelector<T> = (state: TradingSessionStore) => T;
export type AccountSelector<T> = (state: AccountStore) => T;
export type ChartInteractionSelector<T> = (state: ChartInteractionStore) => T;
