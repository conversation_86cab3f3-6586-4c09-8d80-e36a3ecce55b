'use client';

import React, { useEffect, useRef, useState } from 'react';
import { 
  createChart, 
  IChartApi, 
  ISeriesApi, 
  CandlestickSeries, 
  createSeriesMarkers, 
  SeriesMarker, 
  IPriceLine, 
  Time, 
  LineWidth, 
  LineStyle 
} from 'lightweight-charts';
import { CandleData, Position, Order, Timeframe, UpdateMode, ChartOrderPlacement, DragCallbacks } from '@/types/trading';
import ChartContextMenu from './ChartContextMenu';

interface TradingChartProps {
  data: CandleData[];
  baseData?: CandleData[]; // Optional since not used in simplified version
  currentIndex: number;
  positions: Position[];
  orders: Order[];
  onPriceClick?: (price: number) => void;
  onChartOrderPlace?: (orderData: ChartOrderPlacement) => void;
  height?: number;
  enableFibonacci?: boolean;
  timeframe?: Timeframe;
  updateMode?: UpdateMode;
  intraCandleStep?: number;
  dataType?: 'candle' | 'tick';
  precision?: number;
  currentBid?: number;
  currentAsk?: number;
  spread?: number;
  dragCallbacks?: DragCallbacks;
  enableDragAndDrop?: boolean;
}

export default function TradingChart({
  data,
  baseData: _baseData, // Renamed to indicate it's not used
  currentIndex,
  positions,
  orders,
  onPriceClick,
  onChartOrderPlace,
  height = 500,
  enableFibonacci: _enableFibonacci = true, // Renamed to indicate it's not used
  timeframe = 'M1',
  updateMode: _updateMode = 'complete', // Renamed to indicate it's not used
  intraCandleStep: _intraCandleStep = 0, // Renamed to indicate it's not used
  dataType: _dataType = 'candle', // Renamed to indicate it's not used
  precision = 5,
  currentBid,
  currentAsk,
  spread: _spread, // Renamed to indicate it's not used
  dragCallbacks: _dragCallbacks, // Renamed to indicate it's not used
  enableDragAndDrop: _enableDragAndDrop = false // Renamed to indicate it's not used
}: TradingChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const markersRef = useRef<ReturnType<typeof createSeriesMarkers<Time>> | null>(null);
  const currentPriceLineRef = useRef<IPriceLine | null>(null);
  const bidPriceLineRef = useRef<IPriceLine | null>(null);
  const askPriceLineRef = useRef<IPriceLine | null>(null);
  const positionLinesRef = useRef<IPriceLine[]>([]);
  const orderLinesRef = useRef<IPriceLine[]>([]);

  const [isChartReady, setIsChartReady] = useState(false);
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [contextMenuPrice, setContextMenuPrice] = useState(0);

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    console.log('TradingChart: Initializing chart...');

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      crosshair: {
        mode: 1,
      },
      localization: {
        priceFormatter: (price: number) => price.toFixed(precision),
      },
      rightPriceScale: {
        borderColor: '#cccccc',
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
        mode: 0,
        autoScale: true,
        invertScale: false,
        alignLabels: true,
        borderVisible: true,
        entireTextOnly: false,
        visible: true,
        ticksVisible: true,
        minimumWidth: 80,
      },
      timeScale: {
        borderColor: '#cccccc',
        timeVisible: true,
        secondsVisible: timeframe.startsWith('S'),
      },
    });

    // Create candlestick series
    const candlestickSeries = chart.addSeries(CandlestickSeries, {
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    });

    // Create markers instance
    const markers = createSeriesMarkers(candlestickSeries, []);
    markersRef.current = markers;

    // Handle click events
    chart.subscribeClick((param) => {
      if (param.point && onPriceClick) {
        const price = candlestickSeries.coordinateToPrice(param.point.y);
        if (price !== null) {
          onPriceClick(price);
        }
      }
    });

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;
    setIsChartReady(true);

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
      chartRef.current = null;
      candlestickSeriesRef.current = null;
      markersRef.current = null;
      currentPriceLineRef.current = null;
      bidPriceLineRef.current = null;
      askPriceLineRef.current = null;
      positionLinesRef.current = [];
      orderLinesRef.current = [];
      setIsChartReady(false);
    };
  }, [height, onPriceClick, timeframe, precision]);

  // Context menu handler
  useEffect(() => {
    if (!chartContainerRef.current || !onChartOrderPlace || !isChartReady || !candlestickSeriesRef.current) return;

    const handleContextMenu = (event: MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();

      if (!candlestickSeriesRef.current || !chartContainerRef.current) return;

      try {
        const rect = chartContainerRef.current.getBoundingClientRect();
        const y = event.clientY - rect.top;

        if (y < 0 || y > rect.height) return;

        const price = candlestickSeriesRef.current.coordinateToPrice(y);

        if (price !== null && !isNaN(price)) {
          setContextMenuPrice(price);
          setContextMenuPosition({ x: event.clientX, y: event.clientY });
          setContextMenuVisible(true);
        }
      } catch (error) {
        console.error('TradingChart: Error in context menu handler:', error);
      }
    };

    chartContainerRef.current.addEventListener('contextmenu', handleContextMenu);

    return () => {
      if (chartContainerRef.current) {
        chartContainerRef.current.removeEventListener('contextmenu', handleContextMenu);
      }
    };
  }, [onChartOrderPlace, isChartReady]);

  // Update chart data
  useEffect(() => {
    if (!candlestickSeriesRef.current || !isChartReady || data.length === 0) return;

    const chartData = data.slice(0, currentIndex + 1).map(candle => ({
      time: (candle.timestamp / 1000) as Time,
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close,
    }));

    candlestickSeriesRef.current.setData(chartData);
  }, [data, currentIndex, isChartReady]);

  // Update markers for orders and positions
  useEffect(() => {
    if (!markersRef.current || !isChartReady || data.length === 0) return;

    const markers: SeriesMarker<Time>[] = [];

    // Add order markers
    orders.forEach(order => {
      if (order.timestamp && order.timestamp <= (data[currentIndex]?.timestamp || 0)) {
        const candle = data.find(c => c.timestamp >= order.timestamp);
        if (candle) {
          markers.push({
            time: (candle.timestamp / 1000) as Time,
            position: order.type === 'buy' ? 'belowBar' : 'aboveBar',
            color: order.type === 'buy' ? '#26a69a' : '#ef5350',
            shape: 'arrowUp',
            text: `${order.type.toUpperCase()} ${order.size}`,
          });
        }
      }
    });

    markersRef.current.setMarkers(markers);
  }, [orders, positions, currentIndex, data, isChartReady]);

  // Update price lines
  useEffect(() => {
    if (!candlestickSeriesRef.current || !isChartReady) return;

    // Clear existing lines
    positionLinesRef.current.forEach(line => candlestickSeriesRef.current?.removePriceLine(line));
    orderLinesRef.current.forEach(line => candlestickSeriesRef.current?.removePriceLine(line));
    positionLinesRef.current = [];
    orderLinesRef.current = [];

    // Add position lines
    positions.forEach(position => {
      if (!candlestickSeriesRef.current) return;

      // Entry line
      const entryLine = candlestickSeriesRef.current.createPriceLine({
        price: position.entryPrice,
        color: position.type === 'buy' ? '#26a69a' : '#ef5350',
        lineWidth: 2 as LineWidth,
        lineStyle: LineStyle.Solid,
        axisLabelVisible: true,
        title: `${position.type.toUpperCase()} ${position.size}`,
      });
      positionLinesRef.current.push(entryLine);

      // Stop loss line
      if (position.stopLoss) {
        const slLine = candlestickSeriesRef.current.createPriceLine({
          price: position.stopLoss,
          color: '#f44336',
          lineWidth: 1 as LineWidth,
          lineStyle: LineStyle.Dashed,
          axisLabelVisible: true,
          title: 'SL',
        });
        positionLinesRef.current.push(slLine);
      }

      // Take profit line
      if (position.takeProfit) {
        const tpLine = candlestickSeriesRef.current.createPriceLine({
          price: position.takeProfit,
          color: '#4caf50',
          lineWidth: 1 as LineWidth,
          lineStyle: LineStyle.Dashed,
          axisLabelVisible: true,
          title: 'TP',
        });
        positionLinesRef.current.push(tpLine);
      }
    });

    // Add pending order lines
    orders.filter(order => order.status === 'pending').forEach(order => {
      if (!candlestickSeriesRef.current) return;

      const orderLine = candlestickSeriesRef.current.createPriceLine({
        price: order.entryPrice || 0,
        color: '#2196f3',
        lineWidth: 1 as LineWidth,
        lineStyle: LineStyle.Dotted,
        axisLabelVisible: true,
        title: `${order.type.toUpperCase()} ${order.size}`,
      });
      orderLinesRef.current.push(orderLine);
    });
  }, [positions, orders, isChartReady]);

  // Update current price line
  useEffect(() => {
    if (!candlestickSeriesRef.current || !isChartReady) return;

    // Remove existing price lines
    if (currentPriceLineRef.current) {
      candlestickSeriesRef.current.removePriceLine(currentPriceLineRef.current);
    }
    if (bidPriceLineRef.current) {
      candlestickSeriesRef.current.removePriceLine(bidPriceLineRef.current);
    }
    if (askPriceLineRef.current) {
      candlestickSeriesRef.current.removePriceLine(askPriceLineRef.current);
    }

    // Add current bid/ask lines if available
    if (currentBid && currentBid > 0) {
      bidPriceLineRef.current = candlestickSeriesRef.current.createPriceLine({
        price: currentBid,
        color: '#ff9800',
        lineWidth: 2 as LineWidth,
        lineStyle: LineStyle.Solid,
        axisLabelVisible: true,
        title: 'Bid',
      });
    }

    if (currentAsk && currentAsk > 0) {
      askPriceLineRef.current = candlestickSeriesRef.current.createPriceLine({
        price: currentAsk,
        color: '#ff5722',
        lineWidth: 2 as LineWidth,
        lineStyle: LineStyle.Solid,
        axisLabelVisible: true,
        title: 'Ask',
      });
    }
  }, [currentBid, currentAsk, isChartReady]);



  return (
    <div className="relative">
      <div ref={chartContainerRef} className="w-full" style={{ height }} />
      
      {contextMenuVisible && (
        <ChartContextMenu
          isVisible={contextMenuVisible}
          position={contextMenuPosition}
          clickedPrice={contextMenuPrice}
          currentBidAsk={{
            bid: currentBid || 0,
            ask: currentAsk || 0,
            spread: _spread || 0,
            timestamp: Date.now()
          }}
          precision={precision || 5}
          onPlaceOrder={onChartOrderPlace || (() => {})}
          onClose={() => setContextMenuVisible(false)}
        />
      )}
    </div>
  );
}
