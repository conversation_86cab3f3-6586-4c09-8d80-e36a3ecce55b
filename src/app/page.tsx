'use client';

import React from 'react';
import { Play, Pause, Ski<PERSON><PERSON>or<PERSON>, <PERSON><PERSON><PERSON><PERSON>, RotateCcw, Settings } from 'lucide-react';
import CSVImporter from '@/components/CSVImporter';
import TradingChart from '@/components/TradingChart';
import OrderPanel from '@/components/OrderPanel';
import PerformancePanel from '@/components/PerformancePanel';
import TradingHeader from '@/components/TradingHeader';
import SettingsPanel from '@/components/SettingsPanel';
import TimeframeSelector from '@/components/TimeframeSelector';
import UpdateModeSelector from '@/components/UpdateModeSelector';
import NotificationSystem from '@/components/NotificationSystem';
import { useTradingSession, useAccount, useChartInteraction } from '@/hooks';
import { OrderType, ChartOrderPlacement } from '@/types/trading';

export default function Home() {
  // Event handlers
  const handleChartOrderPlace = (orderData: ChartOrderPlacement) => {
    // This will be implemented with the account hook
    console.log('Chart order placement:', orderData);
  };

  const handleDragStart = (lineId: string, lineType: string) => {
    console.log('Drag start:', lineId, lineType);
  };

  const handleDragEnd = (lineId: string, lineType: string, success: boolean) => {
    console.log('Drag end:', lineId, lineType, success);
  };

  // Use custom hooks for state management
  const tradingSession = useTradingSession();
  const account = useAccount();
  const chartInteraction = useChartInteraction({
    onChartOrderPlace: handleChartOrderPlace,
    dragCallbacks: {
      onDragStart: handleDragStart,
      onDragEnd: handleDragEnd
    }
  });

  // Order placement handler
  const handlePlaceOrder = (orderParams: {
    type: OrderType;
    size: number;
    stopLoss?: number;
    takeProfit?: number;
  }) => {
    try {
      account.placeOrder({
        ...orderParams,
        currentBid: tradingSession.currentBid,
        currentAsk: tradingSession.currentAsk,
        precision: tradingSession.precision || 5
      });
    } catch (error) {
      console.error('Failed to place order:', error);
      // TODO: Show error notification
    }
  };

  // Computed values
  const getCurrentStepPrice = (): number => {
    return tradingSession.currentBid || tradingSession.currentAsk || 0;
  };

  // Show importer if no data loaded
  if (tradingSession.showImporter) {
    return (
      <div className="min-h-screen bg-trading-surface">
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-trading-text-primary mb-4">
              FX Backtester
            </h1>
            <p className="text-xl text-trading-text-secondary">
              Professional forex backtesting with MetaTrader data support
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="trading-panel p-6 text-center">
              <div className="w-12 h-12 bg-trading-success/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Play className="h-6 w-6 text-trading-success" />
              </div>
              <h3 className="font-semibold text-trading-text-primary mb-2">Real-time Simulation</h3>
              <p className="text-sm text-trading-text-secondary">
                Tick-by-tick backtesting with realistic market conditions
              </p>
            </div>

            <div className="trading-panel p-6 text-center">
              <div className="w-12 h-12 bg-trading-primary/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <SkipForward className="h-6 w-6 text-trading-primary" />
              </div>
              <h3 className="font-semibold text-trading-text-primary mb-2">Advanced Charts</h3>
              <p className="text-sm text-trading-text-secondary">
                TradingView-powered charts with technical indicators
              </p>
            </div>

            <div className="trading-panel p-6 text-center">
              <div className="w-12 h-12 bg-trading-warning/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Settings className="h-6 w-6 text-trading-warning" />
              </div>
              <h3 className="font-semibold text-trading-text-primary mb-2">Analytics</h3>
              <p className="text-sm text-trading-text-secondary">
                Comprehensive performance metrics and reporting
              </p>
            </div>
          </div>

          <div className="animate-slide-up">
            <CSVImporter onDataLoaded={tradingSession.handleDataLoaded} />
          </div>

          {tradingSession.hasData && (
            <div className="mt-8 text-center animate-fade-in">
              <button
                onClick={() => tradingSession.setShowImporter(false)}
                className="trading-button-primary px-8 py-3 text-lg"
              >
                Start Trading Session
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-trading-surface">
      <TradingHeader
        symbol={tradingSession.symbol}
        totalCandles={tradingSession.totalItems}
        currentBalance={account.account.balance}
        totalPnL={account.account.totalPnL}
        onImportData={() => tradingSession.setShowImporter(true)}
        onSettings={() => tradingSession.setShowSettings(true)}
        isConnected={true}
        currentBid={tradingSession.currentBid}
        currentAsk={tradingSession.currentAsk}
        spread={tradingSession.spread}
        precision={tradingSession.precision}
      />

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chart area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Playback controls */}
            <div className="trading-card">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={tradingSession.stepBackward}
                    disabled={!tradingSession.canStepBackward}
                    className="p-3 rounded-lg bg-trading-surface border border-trading-border hover:bg-trading-accent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 text-trading-text-primary"
                  >
                    <SkipBack className="h-4 w-4" />
                  </button>

                  <button
                    onClick={tradingSession.togglePlayback}
                    disabled={!tradingSession.hasData}
                    className="p-3 rounded-lg bg-trading-primary text-white hover:bg-trading-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {tradingSession.isPlaying ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </button>

                  <button
                    onClick={tradingSession.stepForward}
                    disabled={!tradingSession.canStepForward}
                    className="p-3 rounded-lg bg-trading-surface border border-trading-border hover:bg-trading-accent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 text-trading-text-primary"
                  >
                    <SkipForward className="h-4 w-4" />
                  </button>

                  <button
                    onClick={tradingSession.resetSession}
                    className="p-3 rounded-lg bg-trading-surface border border-trading-border hover:bg-trading-accent transition-all duration-200 text-trading-text-primary"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </button>
                </div>

                <div className="flex items-center space-x-4">
                  <TimeframeSelector
                    currentTimeframe={tradingSession.timeframe}
                    onTimeframeChange={tradingSession.switchTimeframe}
                  />

                  <UpdateModeSelector
                    currentTimeframe={tradingSession.timeframe}
                    currentMode={tradingSession.updateMode}
                    onModeChange={tradingSession.setUpdateMode}
                  />
                </div>
              </div>

              {/* Progress bar */}
              <div className="w-full bg-trading-border rounded-full h-2">
                <div
                  className="bg-trading-primary h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${tradingSession.totalItems > 0 ? (tradingSession.currentIndex / (tradingSession.totalItems - 1)) * 100 : 0}%`
                  }}
                ></div>
              </div>

              <div className="flex justify-between text-sm text-trading-text-secondary mt-2">
                <span>Candle {tradingSession.currentIndex + 1} of {tradingSession.totalItems}</span>
                <span>Speed: {tradingSession.playbackSpeed === 'realtime' ? 'Real-time' : `${tradingSession.playbackSpeed}ms`}</span>
              </div>
            </div>

            {/* Chart */}
            <TradingChart
              data={tradingSession.data}
              baseData={tradingSession.baseData}
              currentIndex={tradingSession.currentIndex}
              positions={account.positions}
              orders={account.orders}
              onChartOrderPlace={handleChartOrderPlace}
              height={500}
              timeframe={tradingSession.timeframe}
              updateMode={tradingSession.updateMode}
              dataType={tradingSession.dataType}
              precision={tradingSession.precision}
              currentBid={tradingSession.currentBid}
              currentAsk={tradingSession.currentAsk}
              spread={tradingSession.spread}
              enableDragAndDrop={chartInteraction.enableDragAndDrop}
              dragCallbacks={{
                onDragStart: handleDragStart,
                onDragEnd: handleDragEnd
              }}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order panel */}
            <OrderPanel
              currentPrice={getCurrentStepPrice()}
              currentBid={tradingSession.currentBid}
              currentAsk={tradingSession.currentAsk}
              spread={tradingSession.spread}
              onPlaceOrder={handlePlaceOrder}
              canTrade={tradingSession.currentIndex < tradingSession.totalItems}
              balance={account.account.balance}
              freeMargin={account.account.freeMargin}
              precision={tradingSession.precision}
            />

            {/* Performance panel */}
            <PerformancePanel />
          </div>
        </div>
      </div>

      {/* Settings panel */}
      {tradingSession.showSettings && (
        <SettingsPanel
          isOpen={tradingSession.showSettings}
          settings={tradingSession.settings}
          onSettingsChange={tradingSession.updateSettings}
          onClose={() => tradingSession.setShowSettings(false)}
        />
      )}

      {/* Notification System */}
      <NotificationSystem />
    </div>
  );
}
